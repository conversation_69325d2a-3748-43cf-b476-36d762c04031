<template>
  <div class="flex items-center justify-center min-h-screen p-4 sm:p-6">
    <div class="flex flex-col items-center justify-center w-full max-w-4xl mx-auto">
      <!-- 火箭图标 -->
      <div class="flex flex-col items-center justify-center mb-6 sm:mb-8">
        <div
             class="flex items-center justify-center mb-4 rounded-full">
          <img src="@/assets/images/logo.png" alt="Logo" class="w-18 h-18">
        </div>

        <!-- 标题 -->
        <div class="px-4 mb-6 text-center sm:mb-8">
          <h1 class="mb-2 text-xl font-bold text-gray-800 sm:text-2xl lg:text-3xl">
            Estimate your academic ability
          </h1>
          <div class="max-w-md mx-auto text-xs font-light text-gray-600 sm:text-sm">
            Enter your X account to see which mutual followers have the same academic ability as you.
          </div>
        </div>

        <!-- 输入框 -->
        <div class="w-full max-w-lg px-4 mb-6 sm:mb-8">
          <UInput
                  v-model="username"
                  @keydown.enter="handleSubmit"
                  @submit="handleSubmit"
                  placeholder="Enter X username"
                  size="lg"
                  class="w-full"
                  :ui="{
                    base: 'relative block disabled:cursor-not-allowed disabled:opacity-75 focus:outline-none w-full h-[56px] sm:h-[64px] flex justify-between items-center bg-white rounded-[80px] border-[4px] border-[#F2F2F259] shadow-[0px_4px_24px_0px_#1226420D] px-4 sm:px-6 py-3',
                    rounded: 'rounded-[80px]',
                    placeholder: 'placeholder-gray-400 dark:placeholder-gray-500',
                    size: {
                      lg: 'text-sm sm:text-base'
                    },
                  }">
            <template #trailing>
              <UButton
                       @click="handleSubmit"
                       :disabled="!username.trim()"
                       variant="ghost"
                       class="active:bg-transparent active:scale-[1.12]"
                       :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                <img src="~/assets/images/ai.png" alt="AI Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
              </UButton>
            </template>
          </UInput>
        </div>

        <!-- 底部导航 -->
        <div class="flex justify-center mb-4 sm:mb-8">
          <div class="flex space-x-3 sm:space-x-4">
            <UButton variant="ghost" square aria-label="X/Twitter"
                     class="active:bg-transparent active:scale-[1.12] p-2 transition-colors rounded-full cursor-pointer sm:p-3 hover:bg-white/20"
                     :ui="{
                      base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent',
                      rounded: 'rounded-full'
                    }">
              <img src="~/assets/images/x.png" alt="X Icon" class="w-6 h-6 sm:w-8 sm:h-8">
            </UButton>
            <UButton variant="ghost" square aria-label="Telegram"
                     class="active:bg-transparent active:scale-[1.12] p-2 transition-colors rounded-full cursor-pointer sm:p-3 hover:bg-white/20"
                     :ui="{
                      base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent',
                      rounded: 'rounded-full'
                    }">
              <img src="~/assets/images/tg.png" alt="Telegram Icon" class="w-6 h-6 sm:w-8 sm:h-8">
            </UButton>
            <UButton variant="ghost" square aria-label="GitHub"
                     class="active:bg-transparent active:scale-[1.12] p-2 transition-colors rounded-full cursor-pointer sm:p-3 hover:bg-white/20"
                     :ui="{
                      base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent',
                      rounded: 'rounded-full'
                    }">
              <img src="~/assets/images/github.png" alt="GitHub Icon" class="w-6 h-6 sm:w-8 sm:h-8">
            </UButton>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
const username = ref('')

const handleSubmit = async () => {
  console.log('submit', username.value)
  let name = username.value.trim()
  const { data } = await useFetch(`/api/profileImage?username=${username.value.trim()}`);
  if (data.value.get_profile_image) {
    // 导航到结果页面
    navigateTo(`/result?username=${encodeURIComponent(name)}&image=${encodeURIComponent(data.value?.get_profile_image || '')}`)
  }
}
</script>

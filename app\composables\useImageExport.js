/**
 * 图片导出功能 Composable
 * 将 ECharts 图表和用户头像合成为图片并导出
 */

export const useImageExport = () => {
  const toast = useToast()

  /**
   * 加载图片并处理跨域问题
   * @param {string} src - 图片源地址
   * @returns {Promise<HTMLImageElement>} 加载完成的图片对象
   */
  const loadImage = (src) => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = 'anonymous' // 处理跨域问题

      img.onload = () => resolve(img)
      img.onerror = (error) => {
        console.error('图片加载失败:', src, error)
        reject(new Error(`图片加载失败: ${src}`))
      }

      // 如果是相对路径或本地路径，直接使用
      // 如果是外部链接，可能需要代理处理
      img.src = src
    })
  }

  /**
   * 创建渐变背景
   * @param {CanvasRenderingContext2D} ctx - Canvas 上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   */
  const drawBackground = (ctx, width, height) => {
    // 先填充白色背景
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, width, height)

    // 创建垂直线性渐变，从上往下
    // 起点：画布顶部中心 (width/2, 0)
    // 终点：画布底部中心 (width/2, height)
    const startX = width / 2
    const startY = 0
    const endX = width / 2
    const endY = height

    // 创建线性渐变
    const gradient = ctx.createLinearGradient(startX, startY, endX, endY)

    // 添加渐变色停
    gradient.addColorStop(0.1168, 'rgba(32, 155, 255, 0.65)') // 11.68%
    gradient.addColorStop(0.4704, 'rgba(63, 181, 247, 0.4)') // 47.04%
    gradient.addColorStop(0.8255, 'rgba(239, 194, 188, 0.35)') // 82.55%

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)
  }

  /**
   * 绘制用户头像（居中在图表区域作为焦点）
   * @param {CanvasRenderingContext2D} ctx - Canvas 上下文
   * @param {HTMLImageElement} avatarImg - 头像图片
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @param {string} username - 用户名（作为备选显示）
   */
  const drawAvatar = (ctx, avatarImg, canvasWidth, canvasHeight, username = '') => {
    const avatarSize = 100 // 头像大小，稍微增大作为焦点
    // 计算头像位置：居中在画布中心（图表中心）
    const avatarX = (canvasWidth - avatarSize) / 2 + 25
    const avatarY = (canvasHeight - avatarSize) / 2 - 25 // 稍微向下偏移，考虑标题空间

    // 保存当前状态
    ctx.save()

    try {
      if (avatarImg) {
        // 创建圆形裁剪路径
        ctx.beginPath()
        ctx.rect(avatarX, avatarY, avatarSize, avatarSize)
        ctx.clip()

        // 绘制头像
        ctx.drawImage(avatarImg, avatarX, avatarY, avatarSize, avatarSize)

        // 恢复状态
        ctx.restore()

        // 绘制头像边框（增强视觉效果）
        ctx.beginPath()
        ctx.rect(avatarX, avatarY, avatarSize, avatarSize)
        ctx.strokeStyle = '#008CFF'
        ctx.lineWidth = 5
        ctx.stroke()

        // 添加外层光晕效果
        ctx.beginPath()
        ctx.rect(avatarX - 3, avatarY - 3, avatarSize + 6, avatarSize + 6)
        ctx.strokeStyle = 'rgba(0, 140, 255, 0.3)'
        ctx.lineWidth = 2
        ctx.stroke()
      } else {
        // 头像加载失败时的备选方案
        ctx.restore()

        // 绘制圆形背景
        ctx.beginPath()
        ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2)
        ctx.fillStyle = '#008CFF'
        ctx.fill()

        // 添加外层光晕效果
        ctx.beginPath()
        ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2 + 3, 0, Math.PI * 2)
        ctx.strokeStyle = 'rgba(0, 140, 255, 0.3)'
        ctx.lineWidth = 2
        ctx.stroke()

        // 绘制用户名首字母
        ctx.fillStyle = '#FFFFFF'
        ctx.font = 'bold 36px Arial' // 稍微增大字体
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        const initial = username ? username.charAt(0).toUpperCase() : '?'
        ctx.fillText(initial, avatarX + avatarSize / 2, avatarY + avatarSize / 2)
      }
    } catch (error) {
      console.error('绘制头像失败:', error)
      ctx.restore()
    }
  }

  /**
   * 导出图片主方法
   * @param {Object} options - 导出选项
   * @param {Object} options.chartInstance - ECharts 实例
   * @param {string} options.avatarSrc - 头像图片源
   * @param {string} options.username - 用户名
   * @param {Object} options.userData - 用户数据
   * @param {string} options.format - 导出格式 ('png' | 'jpeg')
   * @param {number} options.quality - 图片质量 (0-1)
   * @returns {Promise<boolean>} 导出是否成功
   */
  const exportAsImage = async (options) => {
    const { chartInstance, avatarSrc, username = '', userData = {}, format = 'png', quality = 0.9 } = options

    try {
      if (!chartInstance) {
        throw new Error('ECharts 实例不存在')
      }

      // 显示加载提示
      toast.add({
        title: '正在生成图片...',
        description: '请稍候',
        color: 'blue',
        timeout: 2000,
      })

      // 设置画布尺寸
      const canvasWidth = 1200
      const canvasHeight = 1000

      // 创建主画布
      const canvas = document.createElement('canvas')
      canvas.width = canvasWidth
      canvas.height = canvasHeight
      const ctx = canvas.getContext('2d')

      // 绘制背景
      drawBackground(ctx, canvasWidth, canvasHeight)

      // 获取 ECharts 图表的 DataURL
      const chartDataURL = chartInstance.getDataURL({
        type: format,
        pixelRatio: 2, // 高分辨率
        backgroundColor: 'transparent',
      })

      // 加载图表图片
      const chartImg = await loadImage(chartDataURL)

      // 绘制图表（保持正方形比例）
      ctx.drawImage(chartImg, 0, 150)

      // 加载并绘制头像
      let avatarImg = null
      if (avatarSrc) {
        try {
          avatarImg = await loadImage(avatarSrc)
        } catch (error) {
          console.warn('头像加载失败，使用备选方案:', error)
        }
      }

      drawAvatar(ctx, avatarImg, canvasWidth, canvasHeight, username)

      // 导出图片
      const mimeType = format === 'jpeg' ? 'image/jpeg' : 'image/png'
      const dataURL = canvas.toDataURL(mimeType, quality)

      // 创建下载链接
      const link = document.createElement('a')
      link.download = `academic-ability-${username || 'result'}.${format}`
      link.href = dataURL

      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 显示成功提示
      toast.add({
        title: '导出成功',
        description: '图片已保存到下载文件夹',
        color: 'green',
        timeout: 3000,
      })

      return true
    } catch (error) {
      console.error('图片导出失败:', error)
      toast.add({
        title: '导出失败',
        description: error.message || '请稍后重试',
        color: 'red',
        timeout: 5000,
      })
      return false
    }
  }

  /**
   * 生成图片Canvas（用于分享功能）
   * @param {Object} options - 生成选项
   * @param {Object} options.chartInstance - ECharts 实例
   * @param {string} options.avatarSrc - 头像图片源
   * @param {string} options.username - 用户名
   * @param {Object} options.userData - 用户数据
   * @param {string} options.format - 导出格式 ('png' | 'jpeg')
   * @param {number} options.quality - 图片质量 (0-1)
   * @returns {Promise<HTMLCanvasElement|null>} 生成的Canvas对象
   */
  const generateImageCanvas = async (options) => {
    const { chartInstance, avatarSrc, username = '', userData = {}, format = 'png', quality = 0.9 } = options

    try {
      if (!chartInstance) {
        throw new Error('ECharts 实例不存在')
      }

      // 设置画布尺寸
      const canvasWidth = 1200
      const canvasHeight = 1000

      // 创建主画布
      const canvas = document.createElement('canvas')
      canvas.width = canvasWidth
      canvas.height = canvasHeight
      const ctx = canvas.getContext('2d')

      // 绘制背景
      drawBackground(ctx, canvasWidth, canvasHeight)

      // 获取 ECharts 图表的 DataURL
      const chartDataURL = chartInstance.getDataURL({
        type: format,
        pixelRatio: 2, // 高分辨率
      })

      // 加载图表图片
      const chartImg = await loadImage(chartDataURL)

      // 绘制图表（保持正方形比例）
      ctx.drawImage(chartImg, 0, 150)

      // 加载并绘制头像
      let avatarImg = null
      if (avatarSrc) {
        try {
          avatarImg = await loadImage(avatarSrc)
        } catch (error) {
          console.warn('头像加载失败，使用备选方案:', error)
        }
      }

      drawAvatar(ctx, avatarImg, canvasWidth, canvasHeight, username)

      return canvas
    } catch (error) {
      console.error('生成图片Canvas失败:', error)
      return null
    }
  }

  return {
    exportAsImage,
    generateImageCanvas,
    loadImage,
    drawBackground,
    drawAvatar,
  }
}

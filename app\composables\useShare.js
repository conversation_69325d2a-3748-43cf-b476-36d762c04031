/**
 * 分享功能 Composable
 * 提供社交媒体分享功能，主要支持 Twitter/X 分享
 */

export const useShare = () => {
  const toast = useToast()

  /**
   * 生成分享文本
   * @param {Object} data - 用户评估数据
   * @param {string} data.username - 用户名
   * @param {number} data.score - 综合分数
   * @param {string} data.assessment - 评估描述
   * @returns {string} 格式化的分享文本
   */
  const generateShareText = (data) => {
    const { username, score, assessment } = data

    // 获取称号
    const getTitle = (score) => {
      if (score <= 3) return '学生'
      if (score === 4) return '助理'
      if (score === 5) return '学者'
      if (score === 6) return '研究员'
      if (score === 7) return '专家'
      return '教授'
    }

    // 获取称号对应的emoji
    const getTitleEmoji = (score) => {
      if (score <= 3) return '🧑‍🎓'
      if (score > 3 && score <= 4) return '🔧'
      if (score > 4 && score <= 5) return '🧑‍🔬'
      if (score > 5 && score <= 6) return '👩‍🔬'
      if (score > 6 && score <= 7) return '🧑‍🏫'
      return '👨‍🎓'
    }

    // 构建分享文本 - 使用你提供的格式
    const shareText = `刚做完 #SCAI 学力评估，得分 ${score} 分，我获得「${getTitle(score)}」称号${getTitleEmoji(score)} ！

对知识的探求无关于你的履历，你的数据足迹就能证明你的价值！

@SCAI_Agents

快来测试你的学术能力吧！ 👉 `

    return shareText
  }

  /**
   * 分享到 Twitter/X
   * @param {Object} shareData - 分享数据
   * @param {string} shareData.text - 分享文本
   * @param {string} shareData.url - 分享链接（可选）
   * @param {string} shareData.hashtags - 标签（可选）
   */
  const shareToTwitter = (shareData) => {
    try {
      const { text, url = window.location.href, hashtags = 'AcademicAbility,EstimateAbility' } = shareData

      // 构建 Twitter 分享 URL
      const twitterUrl = new URL('https://twitter.com/intent/tweet')
      twitterUrl.searchParams.set('text', text)
      twitterUrl.searchParams.set('url', url)
      if (hashtags) {
        twitterUrl.searchParams.set('hashtags', hashtags)
      }

      // 打开分享窗口
      const shareWindow = window.open(twitterUrl.toString(), 'twitter-share', 'width=550,height=420,resizable=yes,scrollbars=yes')

      // 检查窗口是否成功打开
      if (!shareWindow) {
        throw new Error('弹窗被阻止，请允许弹窗后重试')
      }

      // 显示成功提示
      toast.add({
        title: '分享窗口已打开',
        description: '请在新窗口中完成分享',
        color: 'green',
        timeout: 3000,
      })

      return true
    } catch (error) {
      console.error('Twitter 分享失败:', error)
      toast.add({
        title: '分享失败',
        description: error.message || '无法打开分享窗口',
        color: 'red',
        timeout: 5000,
      })
      return false
    }
  }

  /**
   * 使用 Web Share API 分享（移动端优先）
   * @param {Object} shareData - 分享数据
   */
  const shareWithWebAPI = async (shareData) => {
    try {
      if (!navigator.share) {
        // 回退到 Twitter 分享
        return shareToTwitter(shareData)
      }

      await navigator.share({
        title: '学术能力评估结果',
        text: shareData.text,
        url: shareData.url || window.location.href,
      })

      toast.add({
        title: '分享成功',
        description: '内容已成功分享',
        color: 'green',
        timeout: 3000,
      })

      return true
    } catch (error) {
      if (error.name === 'AbortError') {
        // 用户取消分享，不显示错误
        return false
      }

      console.error('Web Share API 分享失败:', error)
      // 回退到 Twitter 分享
      return shareToTwitter(shareData)
    }
  }

  /**
   * 复制分享链接到剪贴板
   * @param {string} url - 要复制的链接
   */
  const copyShareLink = async (url = window.location.href) => {
    try {
      await navigator.clipboard.writeText(url)
      toast.add({
        title: '链接已复制',
        description: '分享链接已复制到剪贴板',
        color: 'green',
        timeout: 3000,
      })
      return true
    } catch (error) {
      console.error('复制链接失败:', error)
      toast.add({
        title: '复制失败',
        description: '无法复制链接到剪贴板',
        color: 'red',
        timeout: 3000,
      })
      return false
    }
  }

  /**
   * 将Canvas转换为Blob对象
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {string} format - 图片格式
   * @param {number} quality - 图片质量
   * @returns {Promise<Blob>} 图片Blob对象
   */
  const canvasToBlob = (canvas, format = 'png', quality = 0.9) => {
    return new Promise((resolve) => {
      canvas.toBlob(resolve, `image/${format}`, quality)
    })
  }

  /**
   * 从Canvas下载图片
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {string} filename - 文件名
   */
  const downloadImageFromCanvas = async (canvas, filename) => {
    const dataURL = canvas.toDataURL('image/png', 0.9)
    const link = document.createElement('a')
    link.download = filename
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  /**
   * 带图片的分享方法
   * @param {Object} userData - 用户评估数据
   * @param {HTMLCanvasElement} imageCanvas - 生成的图片Canvas
   */
  const shareWithImage = async (userData, imageCanvas) => {
    try {
      const shareText = generateShareText(userData)

      // 检查是否支持Web Share API和文件分享
      if (navigator.share && navigator.canShare) {
        try {
          // 将Canvas转换为Blob
          const imageBlob = await canvasToBlob(imageCanvas, 'png', 0.9)
          const imageFile = new File([imageBlob], `scai-result-${userData.username || 'user'}.png`, {
            type: 'image/png',
          })

          // 检查是否可以分享文件
          const shareData = {
            title: 'SCAI 学术能力评估结果',
            text: shareText,
            files: [imageFile],
          }

          if (navigator.canShare(shareData)) {
            await navigator.share(shareData)
            toast.add({
              title: '分享成功',
              description: '图片和文本已成功分享',
              color: 'green',
              timeout: 3000,
            })
            return true
          }
        } catch (error) {
          console.warn('Web Share API 分享失败，回退到其他方式:', error)
        }
      }

      // 回退方案：先下载图片，然后分享文本
      await downloadImageFromCanvas(imageCanvas, `scai-result-${userData.username || 'user'}.png`)

      toast.add({
        title: '图片已下载',
        description: '现在将打开分享窗口',
        color: 'blue',
        timeout: 3000,
      })

      // 然后分享文本
      const shareData = {
        text: shareText,
        url: window.location.href,
        hashtags: 'SCAI,AcademicAbility',
      }

      return shareToTwitter(shareData)
    } catch (error) {
      console.error('带图片分享失败:', error)
      toast.add({
        title: '分享失败',
        description: error.message || '请稍后重试',
        color: 'red',
        timeout: 5000,
      })
      return false
    }
  }

  /**
   * 检测设备类型和分享能力
   * @returns {Object} 设备信息和分享能力
   */
  const detectShareCapabilities = (debug = false) => {
    const userAgent = navigator.userAgent
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || window.innerWidth <= 768
    const hasWebShare = 'share' in navigator
    const hasCanShare = 'canShare' in navigator
    const supportsFileShare = hasWebShare && hasCanShare

    const capabilities = {
      isMobile,
      hasWebShare,
      hasCanShare,
      supportsFileShare,
      userAgent: userAgent.substring(0, 100) + '...', // 截取前100字符
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
    }

    if (debug) {
      console.log('🔍 分享能力检测结果:', capabilities)
    }

    return capabilities
  }

  /**
   * 智能分享路由 - 根据设备和能力选择最佳分享方式
   * @param {Object} userData - 用户评估数据
   * @param {HTMLCanvasElement} imageCanvas - 可选的图片Canvas
   */
  const smartShare = async (userData, imageCanvas = null) => {
    const capabilities = detectShareCapabilities(true) // 启用调试模式

    toast.add({
      title: '正在准备分享...',
      description: capabilities.isMobile ? '即将打开分享面板' : '正在生成分享内容',
      color: 'blue',
      timeout: 2000,
    })

    try {
      // 如果有图片Canvas，优先尝试图片分享
      if (imageCanvas) {
        // 移动端且支持文件分享
        if (capabilities.isMobile && capabilities.supportsFileShare) {
          const success = await shareWithNativeAPI(userData, imageCanvas)
          if (success) return true
        }

        // 降级方案：下载图片 + 分享文本
        return await shareWithFallback(userData, imageCanvas)
      }

      // 纯文本分享
      return await shareTextOnly(userData, capabilities)
    } catch (error) {
      console.error('智能分享失败:', error)

      // 最后的降级方案：复制链接到剪贴板
      try {
        const shareText = generateShareText(userData)
        const fullContent = `${shareText}\n\n链接：${window.location.href}`
        const capabilities = detectShareCapabilities()

        await navigator.clipboard.writeText(fullContent)

        // 根据设备类型提供不同的指导
        if (capabilities.isMobile) {
          toast.add({
            title: '📋 内容已复制到剪贴板！',
            description: '请打开微信、微博等社交应用，长按输入框粘贴分享内容',
            color: 'green',
            timeout: 8000,
          })

          // 延迟显示图片操作提示
          setTimeout(() => {
            toast.add({
              title: '📸 关于图片分享',
              description: '如需分享图片，请在相册中找到刚才保存的SCAI评估结果图片',
              color: 'blue',
              timeout: 8000,
            })
          }, 3000)
        } else {
          toast.add({
            title: '📋 内容已复制到剪贴板！',
            description: '请打开Twitter、Facebook等社交平台，粘贴分享内容',
            color: 'green',
            timeout: 6000,
          })

          // 延迟显示图片操作提示
          setTimeout(() => {
            toast.add({
              title: '🖼️ 图片分享提示',
              description: '图片已下载到本地，可在社交平台中手动上传或拖拽添加',
              color: 'blue',
              timeout: 8000,
            })
          }, 3000)
        }

        return true
      } catch (clipboardError) {
        console.error('复制到剪贴板也失败了:', clipboardError)
        toast.add({
          title: '❌ 分享功能暂时不可用',
          description: '请手动复制页面链接，或截图分享评估结果',
          color: 'red',
          timeout: 8000,
        })
        return false
      }
    }
  }

  /**
   * 原生API分享（移动端优先）
   * @param {Object} userData - 用户数据
   * @param {HTMLCanvasElement} imageCanvas - 图片Canvas
   */
  const shareWithNativeAPI = async (userData, imageCanvas) => {
    try {
      const shareText = generateShareText(userData)
      const imageBlob = await canvasToBlob(imageCanvas, 'png', 0.9)
      const imageFile = new File([imageBlob], `scai-result-${userData.username || 'user'}.png`, {
        type: 'image/png',
      })

      const shareData = {
        title: 'SCAI 学术能力评估结果',
        text: shareText,
        files: [imageFile],
      }

      if (navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData)
        toast.add({
          title: '分享成功！',
          description: '图片和文本已成功分享',
          color: 'green',
          timeout: 3000,
        })
        return true
      }
    } catch (error) {
      console.warn('原生API分享失败:', error)
    }
    return false
  }

  /**
   * 显示详细的操作指导
   * @param {Object} capabilities - 设备能力信息
   * @param {string} filename - 下载的文件名
   */
  const showDetailedInstructions = (capabilities, filename) => {
    if (capabilities.isMobile) {
      // 移动端指导
      toast.add({
        title: '📱 图片已保存到相册！',
        description: '即将打开分享窗口，请点击"添加图片"按钮选择刚才保存的图片',
        color: 'blue',
        timeout: 8000, // 延长显示时间
      })

      // 延迟显示第二个提示
      setTimeout(() => {
        toast.add({
          title: '💡 操作提示',
          description: '在分享窗口中：1️⃣ 点击图片图标 2️⃣ 选择刚保存的图片 3️⃣ 发布分享',
          color: 'green',
          timeout: 10000,
        })
      }, 2000)
    } else {
      // 桌面端指导
      toast.add({
        title: '💻 图片已下载到本地！',
        description: `文件名：${filename}，即将打开Twitter分享窗口`,
        color: 'blue',
        timeout: 6000,
      })

      // 延迟显示操作指导
      setTimeout(() => {
        toast.add({
          title: '🖱️ 操作提示',
          description: '将下载的图片直接拖拽到Twitter分享窗口中，或点击"添加媒体"按钮选择图片',
          color: 'green',
          timeout: 10000,
        })
      }, 2000)
    }
  }

  /**
   * 降级分享方案
   * @param {Object} userData - 用户数据
   * @param {HTMLCanvasElement} imageCanvas - 图片Canvas
   */
  const shareWithFallback = async (userData, imageCanvas) => {
    try {
      const filename = `scai-result-${userData.username || 'user'}.png`
      const capabilities = detectShareCapabilities()

      // 先下载图片
      await downloadImageFromCanvas(imageCanvas, filename)

      // 显示详细的操作指导
      showDetailedInstructions(capabilities, filename)

      // 等待用户阅读提示后再打开分享窗口
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // 然后分享文本
      const shareText = generateShareText(userData)
      const shareData = {
        text: shareText,
        url: window.location.origin,
        hashtags: 'SCAI,AcademicAbility',
      }

      if (capabilities.isMobile && capabilities.hasWebShare) {
        return await shareWithWebAPI(shareData)
      } else {
        return shareToTwitter(shareData)
      }
    } catch (error) {
      console.error('降级分享失败:', error)
      return false
    }
  }

  /**
   * 纯文本分享
   * @param {Object} userData - 用户数据
   * @param {Object} capabilities - 设备能力
   */
  const shareTextOnly = async (userData, capabilities) => {
    const shareText = generateShareText(userData)
    const shareData = {
      text: shareText,
      url: window.location.href,
      hashtags: 'AcademicAbility,EstimateAbility',
    }

    if (capabilities.isMobile && capabilities.hasWebShare) {
      return await shareWithWebAPI(shareData)
    } else {
      return shareToTwitter(shareData)
    }
  }

  /**
   * 主要分享方法 - 智能选择分享方式
   * @param {Object} userData - 用户评估数据
   * @param {HTMLCanvasElement} imageCanvas - 可选的图片Canvas
   */
  const share = async (userData, imageCanvas = null) => {
    return await smartShare(userData, imageCanvas)
  }

  return {
    share,
    smartShare,
    shareToTwitter,
    shareWithWebAPI,
    shareWithImage,
    shareWithNativeAPI,
    shareWithFallback,
    shareTextOnly,
    showDetailedInstructions,
    copyShareLink,
    generateShareText,
    canvasToBlob,
    downloadImageFromCanvas,
    detectShareCapabilities,
  }
}

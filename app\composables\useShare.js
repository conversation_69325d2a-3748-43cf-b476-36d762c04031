/**
 * 分享功能 Composable
 * 提供社交媒体分享功能，主要支持 Twitter/X 分享
 */

export const useShare = () => {
  const toast = useToast()

  /**
   * 生成分享文本
   * @param {Object} data - 用户评估数据
   * @param {string} data.username - 用户名
   * @param {number} data.score - 综合分数
   * @param {string} data.assessment - 评估描述
   * @returns {string} 格式化的分享文本
   */
  const generateShareText = (data) => {
    const { username, score, assessment } = data

    // 获取称号
    const getTitle = (score) => {
      if (score <= 3) return '学生👶'
      if (score === 4) return '助理🧑‍💻'
      if (score === 5) return '学者👩‍💼'
      if (score === 6) return '研究员🧑‍🎓'
      if (score === 7) return '专家👩‍🔬'
      return '教授👨‍🏫'
    }

    // 构建分享文本 - 使用你提供的格式
    const shareText = `刚做完 #SCAI 学力评估，得分 ${score} 分，我获得「${getTitle(score)}」称号！

对知识的探求无关于你的履历，你的数据足迹就能证明你的价值！

@SCAI_Agents

快来测试你的学术能力吧！ 👉 ${window.location.origin}`

    return shareText
  }

  /**
   * 分享到 Twitter/X
   * @param {Object} shareData - 分享数据
   * @param {string} shareData.text - 分享文本
   * @param {string} shareData.url - 分享链接（可选）
   * @param {string} shareData.hashtags - 标签（可选）
   */
  const shareToTwitter = (shareData) => {
    try {
      const { text, url = window.location.href, hashtags = 'AcademicAbility,EstimateAbility' } = shareData

      // 构建 Twitter 分享 URL
      const twitterUrl = new URL('https://twitter.com/intent/tweet')
      twitterUrl.searchParams.set('text', text)
      twitterUrl.searchParams.set('url', url)
      if (hashtags) {
        twitterUrl.searchParams.set('hashtags', hashtags)
      }

      // 打开分享窗口
      const shareWindow = window.open(twitterUrl.toString(), 'twitter-share', 'width=550,height=420,resizable=yes,scrollbars=yes')

      // 检查窗口是否成功打开
      if (!shareWindow) {
        throw new Error('弹窗被阻止，请允许弹窗后重试')
      }

      // 显示成功提示
      toast.add({
        title: '分享窗口已打开',
        description: '请在新窗口中完成分享',
        color: 'green',
        timeout: 3000,
      })

      return true
    } catch (error) {
      console.error('Twitter 分享失败:', error)
      toast.add({
        title: '分享失败',
        description: error.message || '无法打开分享窗口',
        color: 'red',
        timeout: 5000,
      })
      return false
    }
  }

  /**
   * 使用 Web Share API 分享（移动端优先）
   * @param {Object} shareData - 分享数据
   */
  const shareWithWebAPI = async (shareData) => {
    try {
      if (!navigator.share) {
        // 回退到 Twitter 分享
        return shareToTwitter(shareData)
      }

      await navigator.share({
        title: '学术能力评估结果',
        text: shareData.text,
        url: shareData.url || window.location.href,
      })

      toast.add({
        title: '分享成功',
        description: '内容已成功分享',
        color: 'green',
        timeout: 3000,
      })

      return true
    } catch (error) {
      if (error.name === 'AbortError') {
        // 用户取消分享，不显示错误
        return false
      }

      console.error('Web Share API 分享失败:', error)
      // 回退到 Twitter 分享
      return shareToTwitter(shareData)
    }
  }

  /**
   * 复制分享链接到剪贴板
   * @param {string} url - 要复制的链接
   */
  const copyShareLink = async (url = window.location.href) => {
    try {
      await navigator.clipboard.writeText(url)
      toast.add({
        title: '链接已复制',
        description: '分享链接已复制到剪贴板',
        color: 'green',
        timeout: 3000,
      })
      return true
    } catch (error) {
      console.error('复制链接失败:', error)
      toast.add({
        title: '复制失败',
        description: '无法复制链接到剪贴板',
        color: 'red',
        timeout: 3000,
      })
      return false
    }
  }

  /**
   * 将Canvas转换为Blob对象
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {string} format - 图片格式
   * @param {number} quality - 图片质量
   * @returns {Promise<Blob>} 图片Blob对象
   */
  const canvasToBlob = (canvas, format = 'png', quality = 0.9) => {
    return new Promise((resolve) => {
      canvas.toBlob(resolve, `image/${format}`, quality)
    })
  }

  /**
   * 从Canvas下载图片
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {string} filename - 文件名
   */
  const downloadImageFromCanvas = async (canvas, filename) => {
    const dataURL = canvas.toDataURL('image/png', 0.9)
    const link = document.createElement('a')
    link.download = filename
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  /**
   * 带图片的分享方法
   * @param {Object} userData - 用户评估数据
   * @param {HTMLCanvasElement} imageCanvas - 生成的图片Canvas
   */
  const shareWithImage = async (userData, imageCanvas) => {
    try {
      const shareText = generateShareText(userData)

      // 检查是否支持Web Share API和文件分享
      if (navigator.share && navigator.canShare) {
        try {
          // 将Canvas转换为Blob
          const imageBlob = await canvasToBlob(imageCanvas, 'png', 0.9)
          const imageFile = new File([imageBlob], `scai-result-${userData.username || 'user'}.png`, {
            type: 'image/png',
          })

          // 检查是否可以分享文件
          const shareData = {
            title: 'SCAI 学术能力评估结果',
            text: shareText,
            files: [imageFile],
          }

          if (navigator.canShare(shareData)) {
            await navigator.share(shareData)
            toast.add({
              title: '分享成功',
              description: '图片和文本已成功分享',
              color: 'green',
              timeout: 3000,
            })
            return true
          }
        } catch (error) {
          console.warn('Web Share API 分享失败，回退到其他方式:', error)
        }
      }

      // 回退方案：先下载图片，然后分享文本
      await downloadImageFromCanvas(imageCanvas, `scai-result-${userData.username || 'user'}.png`)

      toast.add({
        title: '图片已下载',
        description: '现在将打开分享窗口',
        color: 'blue',
        timeout: 3000,
      })

      // 然后分享文本
      const shareData = {
        text: shareText,
        url: window.location.href,
        hashtags: 'SCAI,AcademicAbility',
      }

      return shareToTwitter(shareData)
    } catch (error) {
      console.error('带图片分享失败:', error)
      toast.add({
        title: '分享失败',
        description: error.message || '请稍后重试',
        color: 'red',
        timeout: 5000,
      })
      return false
    }
  }

  /**
   * 主要分享方法 - 智能选择分享方式
   * @param {Object} userData - 用户评估数据
   * @param {HTMLCanvasElement} imageCanvas - 可选的图片Canvas
   */
  const share = async (userData, imageCanvas = null) => {
    // 如果提供了图片Canvas，使用带图片的分享
    if (imageCanvas) {
      return await shareWithImage(userData, imageCanvas)
    }

    // 否则只分享文本
    try {
      // 生成分享文本
      const shareText = generateShareText(userData)

      const shareData = {
        text: shareText,
        url: window.location.href,
        hashtags: 'AcademicAbility,EstimateAbility',
      }

      // 移动端优先使用 Web Share API
      if (window.innerWidth <= 768 && navigator.share) {
        return await shareWithWebAPI(shareData)
      } else {
        // 桌面端使用 Twitter 分享
        return shareToTwitter(shareData)
      }
    } catch (error) {
      console.error('分享失败:', error)
      toast.add({
        title: '分享失败',
        description: '请稍后重试',
        color: 'red',
        timeout: 3000,
      })
      return false
    }
  }

  return {
    share,
    shareToTwitter,
    shareWithWebAPI,
    shareWithImage,
    copyShareLink,
    generateShareText,
    canvasToBlob,
    downloadImageFromCanvas,
  }
}
